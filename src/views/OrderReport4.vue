<template>
  <v-container fluid class="pa-4">
    <!-- 品牌选择区域 -->
    <v-card class="mb-4" elevation="2">
      <v-card-text>
        <v-row align="center">
          <v-col cols="12" md="4">
            <v-select
              v-model="selectedBrand"
              :items="brandOptions"
              label="当前品牌"
              variant="outlined"
              density="compact"
              hide-details
              @update:model-value="onBrandChange"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 筛选条件区域 -->
    <v-card class="mb-4" elevation="2">
      <v-card-title class="text-h6">订单筛选</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="3">
            <v-text-field
              v-model="filters.startDate"
              label="开始日期"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="3">
            <v-text-field
              v-model="filters.endDate"
              label="结束日期"
              type="date"
              variant="outlined"
              density="compact"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="3">
            <v-checkbox
              v-model="filters.last24Hours"
              label="24小时内的订单"
              hide-details
            />
          </v-col>
          <v-col cols="12" md="3">
            <v-btn
              color="primary"
              variant="elevated"
              @click="applyFilters"
            >
              应用筛选
            </v-btn>
          </v-col>
        </v-row>
        
        <v-row class="mt-2">
          <v-col cols="12">
            <v-radio-group v-model="filters.status" inline hide-details>
              <v-radio label="处理中" value="processing" />
              <v-radio label="所有" value="all" />
              <v-radio label="已完成" value="completed" />
              <v-radio label="已取消" value="cancelled" />
            </v-radio-group>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- 订单列表 -->
    <div v-for="order in filteredOrders" :key="order.id" class="mb-4">
      <v-card elevation="3">
        <!-- 订单头部 -->
        <v-card-title class="d-flex align-center pa-3 bg-grey-lighten-4">
          <v-chip
            :color="getVersionColor(order.version)"
            variant="elevated"
            size="small"
            class="me-3"
          >
            {{ order.version }}
          </v-chip>
          <span class="text-h6 me-4">#{{ order.id }}</span>
          <span class="text-body-2">{{ order.title }}</span>
          <v-spacer />
          <span class="text-caption">
            商家: {{ order.store.name }}, 电话: {{ order.store.phone }}
          </span>
        </v-card-title>

        <v-divider />

        <!-- 订单内容 -->
        <v-card-text class="pa-4">
          <v-row>
            <!-- 左侧信息 -->
            <v-col cols="12" md="6">
              <v-list density="compact">
                <v-list-item>
                  <template #prepend>
                    <v-icon>mdi-receipt</v-icon>
                  </template>
                  <v-list-item-title>单号: {{ order.orderNumber }}</v-list-item-title>
                  <template #append>
                    <v-btn icon size="small" variant="text">
                      <v-icon>mdi-content-copy</v-icon>
                    </v-btn>
                  </template>
                </v-list-item>
                
                <v-list-item>
                  <template #prepend>
                    <v-icon>mdi-source-branch</v-icon>
                  </template>
                  <v-list-item-title>来源: {{ order.source }}</v-list-item-title>
                </v-list-item>
                
                <v-list-item>
                  <template #prepend>
                    <v-icon>mdi-account</v-icon>
                  </template>
                  <v-list-item-title>收款负责人: {{ order.recipient }}</v-list-item-title>
                </v-list-item>
                
                <v-list-item>
                  <template #prepend>
                    <v-icon>mdi-clock</v-icon>
                  </template>
                  <v-list-item-title>下单时间: {{ order.orderTime }}</v-list-item-title>
                </v-list-item>
                
                <v-list-item>
                  <template #prepend>
                    <v-icon>mdi-truck-delivery</v-icon>
                  </template>
                  <v-list-item-title>预计送达: {{ order.deliveryTime }}</v-list-item-title>
                </v-list-item>
              </v-list>
            </v-col>

            <!-- 右侧信息 -->
            <v-col cols="12" md="6">
              <!-- 商家信息 -->
              <v-card variant="outlined" class="mb-3">
                <v-card-subtitle>商家信息</v-card-subtitle>
                <v-card-text class="pt-0">
                  <p><strong>{{ order.store.name }}</strong></p>
                  <p>电话: {{ order.store.phone }}</p>
                  <p>地址: {{ order.store.address }}</p>
                  <p>会员ID: {{ order.store.memberId }}, 第{{ order.store.orderCount }}次下单</p>
                </v-card-text>
              </v-card>

              <!-- 商品列表 -->
              <v-card variant="outlined" class="mb-3">
                <v-card-subtitle>订单商品</v-card-subtitle>
                <v-card-text class="pt-0">
                  <v-list density="compact">
                    <v-list-item
                      v-for="(item, index) in order.items"
                      :key="index"
                      class="px-0"
                    >
                      <v-list-item-title>{{ item.name }}</v-list-item-title>
                      <v-list-item-subtitle v-if="item.qty">{{ item.qty }}</v-list-item-subtitle>
                      <template #append>
                        <span class="font-weight-bold">${{ item.price.toFixed(2) }}</span>
                      </template>
                    </v-list-item>
                  </v-list>
                </v-card-text>
              </v-card>

              <!-- 价格明细 -->
              <v-card variant="outlined">
                <v-card-subtitle>价格明细</v-card-subtitle>
                <v-card-text class="pt-0">
                  <v-list density="compact">
                    <v-list-item class="px-0">
                      <v-list-item-title>堂食小计</v-list-item-title>
                      <template #append>
                        <span>${{ order.totals.subtotal.toFixed(2) }}</span>
                      </template>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-list-item-title>堂食小计(税后)</v-list-item-title>
                      <template #append>
                        <span>${{ order.totals.subtotalWithTax.toFixed(2) }}</span>
                      </template>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-list-item-title>服务费</v-list-item-title>
                      <template #append>
                        <span>${{ order.totals.serviceFee.toFixed(2) }}</span>
                      </template>
                    </v-list-item>
                    <v-list-item class="px-0">
                      <v-list-item-title>小费</v-list-item-title>
                      <template #append>
                        <span>${{ order.totals.tip.toFixed(2) }}</span>
                      </template>
                    </v-list-item>
                    <v-divider class="my-2" />
                    <v-list-item class="px-0">
                      <v-list-item-title class="font-weight-bold">总计</v-list-item-title>
                      <template #append>
                        <span class="font-weight-bold text-primary">
                          ${{ order.totals.total.toFixed(2) }}
                        </span>
                      </template>
                    </v-list-item>
                  </v-list>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <!-- 操作按钮 -->
          <v-row class="mt-3">
            <v-col cols="12" class="d-flex justify-end">
              <v-btn
                color="primary"
                variant="elevated"
                size="small"
                class="me-2"
                @click="viewOrderDetail(order.id)"
              >
                查看详情
              </v-btn>
              <v-btn
                color="success"
                variant="elevated"
                size="small"
                class="me-2"
                @click="printOrder(order.id)"
              >
                打印订单
              </v-btn>
              <v-btn
                color="warning"
                variant="elevated"
                size="small"
                @click="confirmOrder(order.id)"
              >
                确认订单
              </v-btn>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </div>

    <!-- 分页 -->
    <v-row class="mt-4">
      <v-col cols="12" class="d-flex justify-center">
        <v-pagination
          v-model="currentPage"
          :length="totalPages"
          :total-visible="7"
          @update:model-value="loadOrders"
        />
      </v-col>
    </v-row>

    <!-- 加载状态 -->
    <v-overlay v-model="loading" class="align-center justify-center">
      <v-progress-circular
        color="primary"
        indeterminate
        size="64"
      />
    </v-overlay>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const totalPages = ref(5)

// 品牌选择
const selectedBrand = ref('821')
const brandOptions = ref([
  { title: '821: Foodhwy', value: '821' },
  { title: '822: TestBrand', value: '822' }
])

// 筛选条件
const filters = ref({
  startDate: '2025-06-19',
  endDate: '2025-06-19',
  last24Hours: false,
  status: 'processing'
})

// 订单数据
const orders = ref([
  {
    id: 5,
    title: '某的测试小铺 (测试供应链, 勿动)',
    version: 'Ver: 2.1.5',
    store: {
      name: 'Northern Store',
      phone: '18887786666',
      address: 'Baker Lake, NU X0C 0A0, Canada',
      memberId: '01527424',
      orderCount: 27
    },
    orderNumber: '23714443',
    source: 'FOODSUP',
    recipient: '司机',
    orderTime: '06/18 02:41',
    deliveryTime: '03:57 - 04:13',
    items: [
      { name: '测试WMS商品同步123', qty: 'x 5', price: 150.00 },
      { name: '堂食的小计', qty: '', price: 750.00 },
      { name: '堂食的小计(税后)', qty: '', price: 862.31 }
    ],
    totals: {
      subtotal: 750.00,
      subtotalWithTax: 862.31,
      deliveryFee: 0.00,
      serviceFee: 3.90,
      driverFee: 0.00,
      tip: 112.31,
      deposit: 0.00,
      total: 866.21
    }
  },
  {
    id: 6,
    title: '测试餐厅2',
    version: 'Ver: 2.2.1',
    store: {
      name: 'Southern Diner',
      phone: '19998887777',
      address: 'Toronto, ON M5V 3A8, Canada',
      memberId: '01527425',
      orderCount: 15
    },
    orderNumber: '23714444',
    source: 'FOODSUP',
    recipient: '司机',
    orderTime: '06/18 03:15',
    deliveryTime: '04:30 - 04:45',
    items: [
      { name: '汉堡套餐', qty: 'x 2', price: 25.99 },
      { name: '可乐', qty: 'x 2', price: 4.99 }
    ],
    totals: {
      subtotal: 56.97,
      subtotalWithTax: 64.52,
      deliveryFee: 2.99,
      serviceFee: 2.50,
      driverFee: 0.00,
      tip: 8.00,
      deposit: 0.00,
      total: 78.01
    }
  }
])

// 计算属性
const filteredOrders = computed(() => {
  return orders.value.filter(order => {
    if (filters.value.status === 'processing') {
      return true // 假设所有订单都在处理中
    }
    return true
  })
})

// 方法
const onBrandChange = (brandId: string) => {
  console.log('品牌切换:', brandId)
  loadOrders()
}

const applyFilters = () => {
  console.log('应用筛选:', filters.value)
  loadOrders()
}

const loadOrders = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const viewOrderDetail = (orderId: number) => {
  console.log('查看订单详情:', orderId)
  // 这里可以导航到订单详情页面或打开详情弹窗
}

const printOrder = (orderId: number) => {
  console.log('打印订单:', orderId)
  // 这里可以调用打印功能
}

const confirmOrder = (orderId: number) => {
  console.log('确认订单:', orderId)
  // 这里可以调用确认订单的API
}

const getVersionColor = (version: string) => {
  if (version.includes('2.2')) {
    return 'success'
  } else if (version.includes('2.1')) {
    return 'warning'
  }
  return 'primary'
}

// 生命周期
onMounted(() => {
  loadOrders()
})
</script>
