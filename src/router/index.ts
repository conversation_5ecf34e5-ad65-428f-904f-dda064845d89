import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import ProductDetail from '../views/ProductDetail.vue'
import Search from '../views/Search/Search.vue'
import SearchResult from '../views/Search/SearchResult.vue'
import SearchViewAll from '@/views/Search/SearchViewAll.vue'
import ChooseAddress from '../views/User/ChooseAddress.vue'
import WarehouseAddress from '../views/User/WarehouseAddress.vue'
import Cart from '../views/Cart.vue'
import Browse from '../views/Browse.vue'
import Account from '../views/User/Account.vue'
import Setting from '../views/User/Setting.vue'
import Profile from '../views/User/Profile.vue'
import ProfileInfoChange from '@/views/User/ProfileInfoChange.vue'
import ReceiveInvitation from '../views/ReceiveInvitation/ReceiveInvitation.vue'
import ReceiveInvitationSuccess from '../views/ReceiveInvitation/ReceiveInvitationSuccess.vue'
import SelectLang from '@/views/Login/SelectLang.vue'
import PhoneRegister from '@/views/Login/PhoneRegister.vue'
import ConfirmOrder from '@/views/Order/ConfirmOrder.vue'
import ConfirmOrderSuccess from '@/views/Order/ConfirmOrderSuccess.vue'
import MyPaymentMethods from '@/views/User/MyPaymentMethods.vue'
import MyAddress from '@/views/User/MyAddress.vue'
import MyCoupon from '@/views/User/MyCoupon.vue'
import InviteRedeemCenter from '@/views/ReceiveInvitation/InviteRedeemCenter.vue'
import MyFavourite from '@/views/User/MyFavourite.vue'
import MyOrders from '@/views/User/MyOrders.vue'
import MembershipActivate from '@/views/Membership/MembershipActivate.vue'
import MembershipPayment from '@/views/Membership/MembershipPayment.vue'
import MembershipPolicy from '@/views/Membership/MembershipPolicy.vue'
import MembershipTransaction from '@/views/Membership/MembershipTransaction.vue'
import MembershipActivateHistory from '@/views/Membership/MembershipActivateHistory.vue'
import MembershipCancellationReason from '@/views/Membership/CancellationReason.vue'
import MembershipWithdraw from '@/views/Membership/Withdraw.vue'
import OrderReport4 from '@/views/OrderReport4.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home', // name需要与component名称一致
      component: Home
    },
    {
      path: '/product-detail/:id',
      name: 'ProductDetail',
      component: ProductDetail
    },
    {
      path: '/search',
      name: 'Search',
      component: Search
    },
    {
      path: '/search-result',
      name: 'SearchResult',
      component: SearchResult
    },
    {
      path: '/search-view-all',
      name: 'SearchViewAll',
      component: SearchViewAll
    },
    {
      path: '/choose-address',
      name: 'ChooseAddress',
      component: ChooseAddress
    },
    {
      path: '/warehouse-address',
      name: 'WarehouseAddress',
      component: WarehouseAddress
    },
    {
      path: '/browse',
      name: 'Browse',
      component: Browse
    },
    {
      path: '/cart',
      name: 'Cart',
      component: Cart
    },
    {
      path: '/account',
      name: 'Account',
      component: Account
    },
    {
      path: '/setting',
      name: 'Setting',
      component: Setting
    },
    {
      path: '/profile',
      name: 'Profile',
      component: Profile
    },
    {
      // 个人信息修改
      path: '/profile-info-change',
      name: 'ProfileInfoChange',
      component: ProfileInfoChange
    },
    {
      // 受到邀请
      path: '/receive-invitation',
      name: 'ReceiveInvitation',
      component: ReceiveInvitation
    },
    {
      // 受到邀请-成功
      path: '/receive-invitation-success',
      name: 'ReceiveInvitationSuccess',
      component: ReceiveInvitationSuccess
    },
    {
      /* 选择语言 */
      path: '/select-lang',
      name: 'SelectLang',
      component: SelectLang
    },
    {
      /* 手机号注册 */
      path: '/phone-register',
      name: 'PhoneRegister',
      component: PhoneRegister
    },
    {
      /* 确认订单 */
      path: '/confirm-order',
      name: 'ConfirmOrder',
      component: ConfirmOrder
    },
    {
      // 下单成功
      path: '/confirm-order-success',
      name: 'ConfirmOrderSuccess',
      component: ConfirmOrderSuccess
    },
    {
      /* 我的支付方式 */
      path: '/my-payment-methods',
      name: 'MyPaymentMethods',
      component: MyPaymentMethods
    },
    {
      /* 我的地址 */
      path: '/my-address',
      name: 'MyAddress',
      component: MyAddress
    },
    {
      /* 我的优惠券 */
      path: '/my-coupon',
      name: 'MyCoupon',
      component: MyCoupon
    },
    {
      /* 邀请和兑换中心 */
      path: '/invite-redeem-center',
      name: 'InviteRedeemCenter',
      component: InviteRedeemCenter
    },
    {
      /* 我的收藏 */
      path: '/my-favourite',
      name: 'MyFavourite',
      component: MyFavourite
    },
    {
      /* 我的订单 */
      path: '/my-orders',
      name: 'MyOrders',
      component: MyOrders
    },
    {
      /* 激活会员 */
      path: '/membership-activate',
      name: 'MembershipActivate',
      component: MembershipActivate
    },
    {
      /* 会员支付 */
      path: '/membership-payment',
      name: 'MembershipPayment',
      component: MembershipPayment
    },
    {
      /* 会员政策 */
      path: '/membership-policy',
      name: 'MembershipPolicy',
      component: MembershipPolicy
    },
    {
      /* 钱包明细 */
      path: '/membership-transaction',
      name: 'MembershipTransaction',
      component: MembershipTransaction
    },
    {
      /* 会员开通历史记录 */
      path: '/membership-activate-history',
      name: 'MembershipActivateHistory',
      component: MembershipActivateHistory
    },
    {
      /* 会员取消原因 */
      path: '/membership-cancellation-reason',
      name: 'MembershipCancellationReason',
      component: MembershipCancellationReason
    },
    {
      /* 会员提现 */
      path: '/membership-withdraw',
      name: 'MembershipWithdraw',
      component: MembershipWithdraw
    }
  ],
  scrollBehavior(to, _, savedPosition) {
    const noScrollRoutes = ['Home', 'MembershipActivate']
    // console.log("[scrollBehavior]savedPosition:", savedPosition)

    if (noScrollRoutes.includes(to.name as string)) {
      return { top: savedPosition?.top || 0 }
    }

    return { top: 0 }
  }
})

export default router
